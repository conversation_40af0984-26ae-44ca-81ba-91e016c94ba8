#!/bin/bash

echo "Starting CodeQL vulnerability analysis..."

# Step 1: Create CodeQL database
echo "Creating CodeQL database..."
codeql database create app-db --language=javascript --source-root=app/

# Step 2: Run default security queries
echo "Running default security queries..."
codeql database analyze app-db codeql/javascript-queries:Security --format=json --output=results/default-scan.json

# Step 3: Run custom VAL_VAR query (with false positives)
echo "Running custom VAL_VAR query..."
codeql database analyze app-db queries/detect-val-var-vulnerability.ql --format=json --output=results/custom-scan.json

# Step 4: Run refined VAL_VAR query (avoiding false positives)
echo "Running refined VAL_VAR query..."
codeql database analyze app-db queries/detect-val-var-refined.ql --format=json --output=results/refined-scan.json

# Step 5: Display results
echo "=== DEFAULT SECURITY SCAN RESULTS ==="
codeql database analyze app-db codeql/javascript-queries:Security --format=table

echo ""
echo "=== CUSTOM VAL_VAR QUERY RESULTS (with false positives) ==="
codeql database analyze app-db queries/detect-val-var-vulnerability.ql --format=table

echo ""
echo "=== REFINED VAL_VAR QUERY RESULTS (false positives filtered) ==="
codeql database analyze app-db queries/detect-val-var-refined.ql --format=table

echo ""
echo "Analysis complete! Check results/ directory for JSON outputs."
