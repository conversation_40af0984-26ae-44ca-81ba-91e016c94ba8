#!/usr/bin/env pwsh

Write-Host "Starting CodeQL vulnerability analysis..."

# Step 1: Create CodeQL database
Write-Host "Creating CodeQL database..."
codeql database create app-db --language=javascript --source-root=app/

# Step 2: Run default security queries
Write-Host "Running default security queries..."
codeql database analyze app-db codeql/javascript-queries:Security --format=json --output=results/default-scan.json

# Step 3: Run custom VAL_VAR query (with false positives)
Write-Host "Running custom VAL_VAR query..."
codeql database analyze app-db queries/detect-val-var-vulnerability.ql --format=json --output=results/custom-scan.json

# Step 4: Run refined VAL_VAR query (avoiding false positives)
Write-Host "Running refined VAL_VAR query..."
codeql database analyze app-db queries/detect-val-var-refined.ql --format=json --output=results/refined-scan.json

# Step 5: Display results
Write-Host "=== DEFAULT SECURITY SCAN RESULTS ==="
codeql database analyze app-db codeql/javascript-queries:Security --format=table

Write-Host ""
Write-Host "=== CUSTOM VAL_VAR QUERY RESULTS (with false positives) ==="
codeql database analyze app-db queries/detect-val-var-vulnerability.ql --format=table

Write-Host ""
Write-Host "=== REFINED VAL_VAR QUERY RESULTS (false positives filtered) ==="
codeql database analyze app-db queries/detect-val-var-refined.ql --format=table

Write-Host ""
Write-Host "Analysis complete! Check results/ directory for JSON outputs."
