/**
 * @name Refined VAL_VAR vulnerability detection
 * @description Detects malicious VAL_VAR assignments while avoiding false positives
 * @kind problem
 * @problem.severity error
 * @precision high
 * @id js/val-var-vulnerability-refined
 * @tags security
 */

import javascript

predicate isLegitimateContext(AssignmentExpr assign) {
  exists(FunctionDeclStmt func |
    assign.getContainer() = func and
    (
      func.getName().toLowerCase().contains("mask") or
      func.getName().toLowerCase().contains("config") or
      func.getName().toLowerCase().contains("privacy")
    )
  ) or
  exists(VariableDeclaration decl |
    assign.getContainer() = decl and
    decl.getAVariable().getName().toLowerCase().contains("config")
  )
}

from AssignmentExpr assign, Variable var, StringLiteralNode strLit
where
  assign.getTarget() = var.getAnAccess() and
  var.getName() = "VAL_VAR" and
  assign.getRhs() = strLit and
  strLit.getValue().toLowerCase().contains("hacker") and
  not isLegitimateContext(assign)
select assign, "Malicious VAL_VAR assignment detected: " + strLit.getValue()
