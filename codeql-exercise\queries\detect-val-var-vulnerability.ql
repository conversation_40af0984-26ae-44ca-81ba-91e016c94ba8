/**
 * @name Suspicious VAL_VAR assignment
 * @description Detects potentially malicious assignments to VAL_VAR
 * @kind problem
 * @problem.severity warning
 * @precision medium
 * @id js/val-var-vulnerability
 * @tags security
 */

import javascript

from AssignmentExpr assign, Variable var, StringLiteralNode strLit
where
  assign.getTarget() = var.getAnAccess() and
  var.getName() = "VAL_VAR" and
  assign.getRhs() = strLit and
  strLit.getValue().toLowerCase().contains("hacker") and
  not exists(FunctionDeclStmt func |
    func.getName().toLowerCase().contains("mask") or
    func.getName().toLowerCase().contains("config")
  )
select assign, "Suspicious assignment to VAL_VAR variable: " + strLit.getValue()
